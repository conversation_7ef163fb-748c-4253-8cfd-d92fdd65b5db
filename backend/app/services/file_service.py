"""
文件处理服务
负责文件上传、验证、存储和管理
"""

import os
import uuid
import aiofiles
from typing import List, Tuple
from fastapi import UploadFile, HTTPException
from PIL import Image
import mimetypes

from app.core.config import settings, get_upload_path
from app.models.file_upload import FileUpload
from app.models.task import Task
from sqlalchemy.orm import Session


class FileService:
    """
    文件服务类
    提供文件上传、验证、存储等功能
    """
    
    def __init__(self):
        self.upload_path = get_upload_path()
        self.allowed_extensions = settings.allowed_extensions
        self.max_file_size = settings.max_file_size
    
    def _validate_file_extension(self, filename: str) -> bool:
        """
        验证文件扩展名
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否为允许的文件类型
        """
        file_ext = os.path.splitext(filename)[1].lower()
        return file_ext in self.allowed_extensions
    
    def _validate_file_size(self, file_size: int) -> bool:
        """
        验证文件大小
        
        Args:
            file_size: 文件大小（字节）
            
        Returns:
            bool: 是否在允许的大小范围内
        """
        return file_size <= self.max_file_size
    
    def _validate_image_file(self, file_path: str) -> bool:
        """
        验证图片文件的有效性
        使用PIL尝试打开图片，如果成功则说明是有效的图片文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为有效的图片文件
        """
        try:
            with Image.open(file_path) as img:
                img.verify()  # 验证图片完整性
            return True
        except Exception:
            return False
    
    def _generate_unique_filename(self, original_filename: str) -> str:
        """
        生成唯一的文件名
        使用UUID确保文件名唯一性，同时保留原始扩展名
        
        Args:
            original_filename: 原始文件名
            
        Returns:
            str: 唯一的文件名
        """
        file_ext = os.path.splitext(original_filename)[1]
        unique_id = str(uuid.uuid4())
        return f"{unique_id}{file_ext}"
    
    async def save_uploaded_file(
        self, 
        upload_file: UploadFile, 
        task: Task, 
        db: Session
    ) -> FileUpload:
        """
        保存上传的文件
        
        这个方法执行完整的文件上传流程：
        1. 验证文件类型和大小
        2. 生成唯一文件名
        3. 保存文件到磁盘
        4. 验证文件内容（如果是图片）
        5. 创建数据库记录
        
        Args:
            upload_file: FastAPI上传文件对象
            task: 关联的任务对象
            db: 数据库会话
            
        Returns:
            FileUpload: 创建的文件记录
            
        Raises:
            HTTPException: 文件验证失败时抛出异常
        """
        
        # 1. 基础验证
        if not upload_file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        if not self._validate_file_extension(upload_file.filename):
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型。支持的类型：{', '.join(self.allowed_extensions)}"
            )
        
        # 2. 读取文件内容并验证大小
        file_content = await upload_file.read()
        file_size = len(file_content)
        
        if not self._validate_file_size(file_size):
            raise HTTPException(
                status_code=400, 
                detail=f"文件大小超过限制。最大允许：{self.max_file_size / 1024 / 1024:.1f}MB"
            )
        
        # 3. 生成唯一文件名和路径
        unique_filename = self._generate_unique_filename(upload_file.filename)
        file_path = os.path.join(self.upload_path, unique_filename)
        
        # 4. 保存文件到磁盘
        try:
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"文件保存失败：{str(e)}")
        
        # 5. 验证图片文件（如果是图片类型）
        file_ext = os.path.splitext(upload_file.filename)[1].lower()
        if file_ext in ['.png', '.jpg', '.jpeg']:
            if not self._validate_image_file(file_path):
                # 如果图片验证失败，删除已保存的文件
                os.remove(file_path)
                raise HTTPException(status_code=400, detail="无效的图片文件")
        
        # 6. 获取MIME类型
        mime_type, _ = mimetypes.guess_type(upload_file.filename)
        
        # 7. 创建数据库记录
        file_record = FileUpload(
            original_filename=upload_file.filename,
            stored_filename=unique_filename,
            file_path=file_path,
            file_size=file_size,
            file_type=file_ext,
            mime_type=mime_type or "application/octet-stream",
            task_id=task.id
        )
        
        db.add(file_record)
        db.commit()
        db.refresh(file_record)
        
        return file_record
    
    async def save_multiple_files(
        self, 
        upload_files: List[UploadFile], 
        task: Task, 
        db: Session
    ) -> List[FileUpload]:
        """
        批量保存多个上传文件
        
        Args:
            upload_files: 上传文件列表
            task: 关联的任务对象
            db: 数据库会话
            
        Returns:
            List[FileUpload]: 创建的文件记录列表
        """
        saved_files = []
        
        for upload_file in upload_files:
            try:
                file_record = await self.save_uploaded_file(upload_file, task, db)
                saved_files.append(file_record)
            except HTTPException:
                # 如果某个文件上传失败，清理已上传的文件
                for saved_file in saved_files:
                    if os.path.exists(saved_file.file_path):
                        os.remove(saved_file.file_path)
                    db.delete(saved_file)
                db.commit()
                raise
        
        return saved_files
    
    def delete_file(self, file_record: FileUpload, db: Session) -> bool:
        """
        删除文件（包括磁盘文件和数据库记录）
        
        Args:
            file_record: 文件记录
            db: 数据库会话
            
        Returns:
            bool: 是否删除成功
        """
        try:
            # 删除磁盘文件
            if os.path.exists(file_record.file_path):
                os.remove(file_record.file_path)
            
            # 删除数据库记录
            db.delete(file_record)
            db.commit()
            
            return True
        except Exception:
            db.rollback()
            return False


# 创建全局文件服务实例
file_service = FileService()
