"""
任务处理服务
负责管理任务的完整生命周期，协调文件处理和LLM服务
"""

import json
from typing import List, Dict, Any, AsyncGenerator
from sqlalchemy.orm import Session

from app.models.task import Task, TaskStatus
from app.models.file_upload import FileUpload
from app.services.llm_service import llm_service
from app.schemas.task import GeneratedContent


class TaskService:
    """
    任务服务类
    负责任务的创建、处理和状态管理
    """
    
    def create_task(self, user_context: str, db: Session) -> Task:
        """
        创建新任务
        
        Args:
            user_context: 用户提供的上下文信息
            db: 数据库会话
            
        Returns:
            Task: 创建的任务对象
        """
        task = Task(
            user_context=user_context,
            status=TaskStatus.PENDING
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        return task
    
    def get_task_by_id(self, task_id: int, db: Session) -> Task:
        """
        根据ID获取任务
        
        Args:
            task_id: 任务ID
            db: 数据库会话
            
        Returns:
            Task: 任务对象，如果不存在则返回None
        """
        return db.query(Task).filter(Task.id == task_id).first()
    
    def update_task_status(
        self, 
        task: Task, 
        status: TaskStatus, 
        db: Session,
        error_message: str = None
    ) -> None:
        """
        更新任务状态
        
        Args:
            task: 任务对象
            status: 新状态
            db: 数据库会话
            error_message: 错误信息（可选）
        """
        task.status = status
        if error_message:
            task.error_message = error_message
        
        db.commit()
        db.refresh(task)
    
    def save_generated_content(
        self, 
        task: Task, 
        prd_content: str, 
        test_cases_content: str, 
        db: Session
    ) -> None:
        """
        保存生成的内容到数据库
        
        Args:
            task: 任务对象
            prd_content: PRD内容
            test_cases_content: 测试用例内容
            db: 数据库会话
        """
        task.generated_prd = prd_content
        task.generated_test_cases = test_cases_content
        task.status = TaskStatus.COMPLETED
        
        db.commit()
        db.refresh(task)
    
    async def process_task_stream(
        self, 
        task: Task, 
        db: Session
    ) -> AsyncGenerator[GeneratedContent, None]:
        """
        流式处理任务，生成PRD和测试用例
        
        这是核心的任务处理方法，执行完整的分析和生成流程：
        1. 分析上传的文件
        2. 流式生成PRD
        3. 流式生成测试用例
        4. 保存结果到数据库
        
        Args:
            task: 任务对象
            db: 数据库会话
            
        Yields:
            GeneratedContent: 流式生成的内容
        """
        
        try:
            # 更新任务状态为处理中
            self.update_task_status(task, TaskStatus.PROCESSING, db)
            
            # 1. 分析上传的文件
            yield GeneratedContent(
                type="status",
                content="正在分析上传的文件...",
                is_complete=False
            )
            
            image_analyses = await llm_service.analyze_uploaded_images(task.uploaded_files)
            
            yield GeneratedContent(
                type="status",
                content="文件分析完成，开始生成产品需求文档...",
                is_complete=False
            )
            
            # 2. 流式生成PRD
            prd_content = ""
            async for prd_chunk in llm_service.generate_prd_stream(
                task.user_context, 
                image_analyses
            ):
                prd_content += prd_chunk
                yield GeneratedContent(
                    type="prd",
                    content=prd_chunk,
                    is_complete=False
                )
            
            # PRD生成完成
            yield GeneratedContent(
                type="prd",
                content="",
                is_complete=True
            )
            
            yield GeneratedContent(
                type="status",
                content="产品需求文档生成完成，开始生成测试用例...",
                is_complete=False
            )
            
            # 3. 流式生成测试用例
            test_cases_content = ""
            async for test_case_chunk in llm_service.generate_test_cases_stream(
                task.user_context,
                image_analyses,
                prd_content
            ):
                test_cases_content += test_case_chunk
                yield GeneratedContent(
                    type="test_cases",
                    content=test_case_chunk,
                    is_complete=False
                )
            
            # 测试用例生成完成
            yield GeneratedContent(
                type="test_cases",
                content="",
                is_complete=True
            )
            
            # 4. 保存生成的内容到数据库
            self.save_generated_content(task, prd_content, test_cases_content, db)
            
            yield GeneratedContent(
                type="status",
                content="任务处理完成！",
                is_complete=True
            )
            
        except Exception as e:
            # 处理错误
            error_message = f"任务处理失败: {str(e)}"
            self.update_task_status(task, TaskStatus.FAILED, db, error_message)
            
            yield GeneratedContent(
                type="error",
                content=error_message,
                is_complete=True
            )
    
    def get_task_list(
        self, 
        db: Session, 
        skip: int = 0, 
        limit: int = 20
    ) -> List[Task]:
        """
        获取任务列表
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[Task]: 任务列表
        """
        return db.query(Task).order_by(Task.created_at.desc()).offset(skip).limit(limit).all()
    
    def get_task_count(self, db: Session) -> int:
        """
        获取任务总数
        
        Args:
            db: 数据库会话
            
        Returns:
            int: 任务总数
        """
        return db.query(Task).count()
    
    def parse_test_cases_json(self, test_cases_content: str) -> List[Dict[str, Any]]:
        """
        解析测试用例JSON内容
        
        Args:
            test_cases_content: 测试用例的JSON字符串
            
        Returns:
            List[Dict]: 解析后的测试用例列表
        """
        try:
            # 尝试从内容中提取JSON部分
            # LLM可能会在JSON前后添加说明文字
            start_idx = test_cases_content.find('{')
            end_idx = test_cases_content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != 0:
                json_content = test_cases_content[start_idx:end_idx]
                parsed_data = json.loads(json_content)
                
                if 'test_cases' in parsed_data:
                    return parsed_data['test_cases']
                else:
                    return []
            else:
                return []
                
        except (json.JSONDecodeError, ValueError):
            # 如果JSON解析失败，返回空列表
            return []


# 创建全局任务服务实例
task_service = TaskService()
