"""
Excel导出服务
负责将任务结果导出为格式化的Excel文件
"""

import io
from typing import List, Dict, Any
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from datetime import datetime

from app.models.task import Task


class ExcelService:
    """
    Excel服务类
    提供任务报告的Excel导出功能
    """
    
    def __init__(self):
        # 定义样式
        self.header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        self.header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.content_font = Font(name='微软雅黑', size=10)
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        self.center_alignment = Alignment(horizontal='center', vertical='center')
        self.wrap_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    def _apply_header_style(self, cell):
        """应用表头样式"""
        cell.font = self.header_font
        cell.fill = self.header_fill
        cell.border = self.border
        cell.alignment = self.center_alignment
    
    def _apply_content_style(self, cell, wrap_text=False):
        """应用内容样式"""
        cell.font = self.content_font
        cell.border = self.border
        if wrap_text:
            cell.alignment = self.wrap_alignment
        else:
            cell.alignment = Alignment(horizontal='left', vertical='center')
    
    def _create_overview_sheet(self, workbook: Workbook, task: Task) -> None:
        """
        创建任务概览和PRD内容的工作表
        
        Args:
            workbook: Excel工作簿对象
            task: 任务对象
        """
        ws = workbook.active
        ws.title = "任务概览与PRD"
        
        # 设置列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 80
        
        row = 1
        
        # 任务基本信息
        ws.merge_cells(f'A{row}:B{row}')
        ws[f'A{row}'] = "任务基本信息"
        self._apply_header_style(ws[f'A{row}'])
        row += 1
        
        # 任务详情
        task_info = [
            ("任务ID", str(task.id)),
            ("创建时间", task.created_at.strftime('%Y-%m-%d %H:%M:%S')),
            ("任务状态", task.status.value),
            ("用户上下文", task.user_context or "无"),
        ]
        
        for label, value in task_info:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            self._apply_content_style(ws[f'A{row}'])
            self._apply_content_style(ws[f'B{row}'], wrap_text=True)
            row += 1
        
        row += 2  # 空行
        
        # 上传文件信息
        if task.uploaded_files:
            ws.merge_cells(f'A{row}:B{row}')
            ws[f'A{row}'] = "上传文件信息"
            self._apply_header_style(ws[f'A{row}'])
            row += 1
            
            for file_upload in task.uploaded_files:
                file_info = f"文件名: {file_upload.original_filename}\n"
                file_info += f"类型: {file_upload.file_type}\n"
                file_info += f"大小: {file_upload.file_size / 1024:.1f} KB\n"
                file_info += f"上传时间: {file_upload.uploaded_at.strftime('%Y-%m-%d %H:%M:%S')}"
                
                ws[f'A{row}'] = "文件详情"
                ws[f'B{row}'] = file_info
                self._apply_content_style(ws[f'A{row}'])
                self._apply_content_style(ws[f'B{row}'], wrap_text=True)
                
                # 设置行高以适应内容
                ws.row_dimensions[row].height = 60
                row += 1
        
        row += 2  # 空行
        
        # PRD内容
        if task.generated_prd:
            ws.merge_cells(f'A{row}:B{row}')
            ws[f'A{row}'] = "产品需求文档 (PRD)"
            self._apply_header_style(ws[f'A{row}'])
            row += 1
            
            ws.merge_cells(f'A{row}:B{row + 20}')  # 合并多行用于PRD内容
            ws[f'A{row}'] = task.generated_prd
            self._apply_content_style(ws[f'A{row}'], wrap_text=True)
            
            # 设置PRD内容区域的行高
            for i in range(row, row + 21):
                ws.row_dimensions[i].height = 20
    
    def _create_test_cases_sheet(self, workbook: Workbook, test_cases: List[Dict[str, Any]]) -> None:
        """
        创建测试用例的工作表
        
        Args:
            workbook: Excel工作簿对象
            test_cases: 测试用例列表
        """
        ws = workbook.create_sheet(title="测试用例")
        
        # 设置列宽
        column_widths = {
            'A': 15,  # 用例ID
            'B': 20,  # 所属模块
            'C': 30,  # 用例标题
            'D': 25,  # 前置条件
            'E': 40,  # 操作步骤
            'F': 30,  # 预期结果
        }
        
        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width
        
        # 表头
        headers = ["用例ID", "所属模块/功能", "用例标题", "前置条件", "操作步骤", "预期结果"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            self._apply_header_style(cell)
        
        # 测试用例数据
        if not test_cases:
            # 如果没有测试用例，显示提示信息
            ws.merge_cells('A2:F2')
            ws['A2'] = "暂无测试用例数据"
            self._apply_content_style(ws['A2'])
            return
        
        row = 2
        for test_case in test_cases:
            # 处理操作步骤（列表转换为文本）
            steps = test_case.get('steps', [])
            if isinstance(steps, list):
                steps_text = '\n'.join([f"{i+1}. {step}" for i, step in enumerate(steps)])
            else:
                steps_text = str(steps)
            
            # 填充数据
            data = [
                test_case.get('case_id', ''),
                test_case.get('module', ''),
                test_case.get('title', ''),
                test_case.get('precondition', ''),
                steps_text,
                test_case.get('expected_result', '')
            ]
            
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                self._apply_content_style(cell, wrap_text=True)
            
            # 设置行高以适应内容
            ws.row_dimensions[row].height = max(30, len(steps) * 15)
            row += 1
        
        # 添加汇总信息
        row += 2
        ws.merge_cells(f'A{row}:F{row}')
        summary_text = f"测试用例总数: {len(test_cases)} 个 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ws[f'A{row}'] = summary_text
        self._apply_content_style(ws[f'A{row}'])
        ws[f'A{row}'].font = Font(name='微软雅黑', size=10, italic=True)
    
    def generate_task_report(self, task: Task, test_cases: List[Dict[str, Any]]) -> io.BytesIO:
        """
        生成任务报告Excel文件
        
        Args:
            task: 任务对象
            test_cases: 测试用例列表
            
        Returns:
            io.BytesIO: Excel文件的字节流
        """
        
        # 创建工作簿
        workbook = Workbook()
        
        # 创建任务概览和PRD工作表
        self._create_overview_sheet(workbook, task)
        
        # 创建测试用例工作表
        self._create_test_cases_sheet(workbook, test_cases)
        
        # 保存到内存中的字节流
        excel_buffer = io.BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        
        return excel_buffer
    
    def generate_test_cases_only(self, test_cases: List[Dict[str, Any]]) -> io.BytesIO:
        """
        仅生成测试用例Excel文件
        
        Args:
            test_cases: 测试用例列表
            
        Returns:
            io.BytesIO: Excel文件的字节流
        """
        
        workbook = Workbook()
        ws = workbook.active
        ws.title = "测试用例"
        
        self._create_test_cases_sheet(workbook, test_cases)
        
        # 删除默认的空工作表
        if len(workbook.worksheets) > 1:
            workbook.remove(workbook.worksheets[0])
        
        excel_buffer = io.BytesIO()
        workbook.save(excel_buffer)
        excel_buffer.seek(0)
        
        return excel_buffer


# 创建全局Excel服务实例
excel_service = ExcelService()
