"""
大语言模型服务
负责调用各种LLM API，处理图片分析和文本生成
"""

import json
import base64
import asyncio
from typing import List, Dict, Any, AsyncGenerator, Optional
import httpx
from PIL import Image
import io

from app.core.config import settings
from app.models.file_upload import FileUpload


class LLMService:
    """
    大语言模型服务类
    支持多种LLM提供商，提供图片分析和文本生成功能
    """
    
    def __init__(self):
        self.openai_api_key = settings.openai_api_key
        self.openai_base_url = settings.openai_base_url
        self.default_provider = settings.default_llm_provider
    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """
        将图片编码为base64格式
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            str: base64编码的图片数据
        """
        with open(image_path, "rb") as image_file:
            # 使用PIL优化图片大小，减少API调用成本
            image = Image.open(image_file)
            
            # 如果图片太大，进行压缩
            max_size = (1024, 1024)
            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 转换为RGB模式（如果是RGBA）
            if image.mode == 'RGBA':
                image = image.convert('RGB')
            
            # 保存为JPEG格式的字节流
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=85)
            buffer.seek(0)
            
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    async def _call_openai_vision_api(
        self, 
        image_base64: str, 
        prompt: str
    ) -> str:
        """
        调用OpenAI Vision API分析图片
        
        Args:
            image_base64: base64编码的图片
            prompt: 分析提示词
            
        Returns:
            str: 分析结果
        """
        if not self.openai_api_key:
            raise ValueError("OpenAI API密钥未配置")
        
        headers = {
            "Authorization": f"Bearer {self.openai_api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "gpt-4-vision-preview",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}",
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 2000
        }
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                f"{self.openai_base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            
            if response.status_code != 200:
                raise Exception(f"OpenAI API调用失败: {response.status_code} - {response.text}")
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
    
    async def analyze_uploaded_images(self, files: List[FileUpload]) -> List[Dict[str, Any]]:
        """
        分析上传的图片文件
        
        对每个图片文件进行分析，提取关键信息
        
        Args:
            files: 上传的文件列表
            
        Returns:
            List[Dict]: 每个文件的分析结果
        """
        analysis_results = []
        
        # 图片分析的提示词
        image_analysis_prompt = """
        请仔细分析这张图片，并按照以下格式提供结构化的分析结果：

        **图片类型**: [思维导图/流程图/界面截图/其他]
        
        **主要内容**:
        - 核心功能或模块
        - 关键流程步骤
        - 重要的文本信息
        
        **结构信息**:
        - 层级关系
        - 连接关系
        - 逻辑流向
        
        **技术要点**:
        - 涉及的技术组件
        - 数据流向
        - 接口关系
        
        **业务逻辑**:
        - 用户操作流程
        - 业务规则
        - 异常处理
        
        请用中文回答，内容要详细且结构化。
        """
        
        for file_record in files:
            # 只处理图片文件
            if file_record.file_type.lower() in ['.png', '.jpg', '.jpeg']:
                try:
                    # 编码图片
                    image_base64 = self._encode_image_to_base64(file_record.file_path)
                    
                    # 调用Vision API分析
                    analysis_result = await self._call_openai_vision_api(
                        image_base64, 
                        image_analysis_prompt
                    )
                    
                    analysis_results.append({
                        "file_name": file_record.original_filename,
                        "file_type": file_record.file_type,
                        "analysis": analysis_result
                    })
                    
                except Exception as e:
                    analysis_results.append({
                        "file_name": file_record.original_filename,
                        "file_type": file_record.file_type,
                        "analysis": f"图片分析失败: {str(e)}"
                    })
            else:
                # 非图片文件的处理
                analysis_results.append({
                    "file_name": file_record.original_filename,
                    "file_type": file_record.file_type,
                    "analysis": f"文件类型 {file_record.file_type} 暂不支持自动分析，请在上下文中手动描述文件内容。"
                })
        
        return analysis_results
    
    async def _call_openai_chat_stream(
        self, 
        messages: List[Dict[str, str]]
    ) -> AsyncGenerator[str, None]:
        """
        调用OpenAI Chat API进行流式文本生成
        
        Args:
            messages: 对话消息列表
            
        Yields:
            str: 流式生成的文本片段
        """
        if not self.openai_api_key:
            raise ValueError("OpenAI API密钥未配置")
        
        headers = {
            "Authorization": f"Bearer {self.openai_api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "gpt-4-turbo-preview",
            "messages": messages,
            "stream": True,
            "max_tokens": 4000,
            "temperature": 0.7
        }
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            async with client.stream(
                "POST",
                f"{self.openai_base_url}/chat/completions",
                headers=headers,
                json=payload
            ) as response:
                
                if response.status_code != 200:
                    raise Exception(f"OpenAI API调用失败: {response.status_code}")
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # 移除 "data: " 前缀
                        
                        if data == "[DONE]":
                            break
                        
                        try:
                            chunk = json.loads(data)
                            if "choices" in chunk and len(chunk["choices"]) > 0:
                                delta = chunk["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
    
    def _build_prd_generation_prompt(
        self, 
        user_context: str, 
        image_analyses: List[Dict[str, Any]]
    ) -> List[Dict[str, str]]:
        """
        构建PRD生成的提示词
        
        Args:
            user_context: 用户提供的上下文信息
            image_analyses: 图片分析结果
            
        Returns:
            List[Dict]: 格式化的消息列表
        """
        
        # 整合图片分析结果
        image_analysis_text = ""
        if image_analyses:
            image_analysis_text = "\n\n**上传文件分析结果**:\n"
            for i, analysis in enumerate(image_analyses, 1):
                image_analysis_text += f"\n{i}. 文件: {analysis['file_name']}\n"
                image_analysis_text += f"   类型: {analysis['file_type']}\n"
                image_analysis_text += f"   分析: {analysis['analysis']}\n"
        
        system_prompt = """
        你是一位资深的产品经理和业务分析师，擅长根据各种输入材料编写专业的产品需求文档(PRD)。

        请根据用户提供的上下文信息和文件分析结果，生成一份结构化的产品需求文档。

        PRD应该包含以下部分：
        1. **产品概述** - 产品的核心价值和目标
        2. **功能需求** - 详细的功能描述和用户故事
        3. **非功能需求** - 性能、安全、可用性等要求
        4. **用户角色** - 目标用户群体和使用场景
        5. **业务流程** - 关键业务流程描述
        6. **接口需求** - 系统间的接口和数据交互
        7. **约束条件** - 技术约束、业务约束等

        请用专业、清晰的语言编写，确保内容完整且易于理解。
        """
        
        user_prompt = f"""
        **用户上下文信息**:
        {user_context}
        
        {image_analysis_text}
        
        请基于以上信息生成详细的产品需求文档(PRD)。
        """
        
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    def _build_test_cases_generation_prompt(
        self, 
        user_context: str, 
        image_analyses: List[Dict[str, Any]], 
        generated_prd: str
    ) -> List[Dict[str, str]]:
        """
        构建测试用例生成的提示词
        
        Args:
            user_context: 用户提供的上下文信息
            image_analyses: 图片分析结果
            generated_prd: 已生成的PRD内容
            
        Returns:
            List[Dict]: 格式化的消息列表
        """
        
        system_prompt = """
        你是一位资深的软件测试工程师，擅长根据产品需求设计全面的测试用例。

        请根据提供的PRD和相关信息，设计详细的测试用例。测试用例应该覆盖：
        1. **功能测试** - 正常功能验证
        2. **边界测试** - 边界值和临界条件
        3. **异常测试** - 错误处理和异常场景
        4. **集成测试** - 模块间的交互
        5. **用户体验测试** - 易用性和用户流程

        每个测试用例必须包含：
        - 用例ID (格式: TC_模块_序号，如 TC_LOGIN_001)
        - 所属模块/功能
        - 用例标题
        - 前置条件
        - 操作步骤 (详细的步骤列表)
        - 预期结果

        请以JSON格式输出测试用例，方便后续处理。
        """
        
        user_prompt = f"""
        **用户上下文信息**:
        {user_context}
        
        **已生成的PRD内容**:
        {generated_prd}
        
        请基于以上PRD内容设计全面的测试用例，以JSON格式输出。
        
        JSON格式示例：
        {{
            "test_cases": [
                {{
                    "case_id": "TC_LOGIN_001",
                    "module": "用户登录",
                    "title": "正常用户名密码登录",
                    "precondition": "用户已注册且账号状态正常",
                    "steps": [
                        "打开登录页面",
                        "输入正确的用户名",
                        "输入正确的密码",
                        "点击登录按钮"
                    ],
                    "expected_result": "登录成功，跳转到首页"
                }}
            ]
        }}
        """
        
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    async def generate_prd_stream(
        self, 
        user_context: str, 
        image_analyses: List[Dict[str, Any]]
    ) -> AsyncGenerator[str, None]:
        """
        流式生成PRD内容
        
        Args:
            user_context: 用户上下文信息
            image_analyses: 图片分析结果
            
        Yields:
            str: 流式生成的PRD内容片段
        """
        messages = self._build_prd_generation_prompt(user_context, image_analyses)
        
        async for chunk in self._call_openai_chat_stream(messages):
            yield chunk
    
    async def generate_test_cases_stream(
        self, 
        user_context: str, 
        image_analyses: List[Dict[str, Any]], 
        generated_prd: str
    ) -> AsyncGenerator[str, None]:
        """
        流式生成测试用例
        
        Args:
            user_context: 用户上下文信息
            image_analyses: 图片分析结果
            generated_prd: 已生成的PRD内容
            
        Yields:
            str: 流式生成的测试用例内容片段
        """
        messages = self._build_test_cases_generation_prompt(
            user_context, 
            image_analyses, 
            generated_prd
        )
        
        async for chunk in self._call_openai_chat_stream(messages):
            yield chunk


# 创建全局LLM服务实例
llm_service = LLMService()
