"""
任务相关API路由
处理任务创建、处理、查询等操作
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List
import json

from app.db.database import get_db
from app.services.task_service import task_service
from app.services.excel_service import excel_service
from app.schemas.task import (
    TaskCreate, 
    TaskResponse, 
    TaskList, 
    GeneratedContent
)
from app.models.task import Task


router = APIRouter()


@router.post("/create", response_model=TaskResponse)
async def create_task(
    task_data: TaskCreate,
    db: Session = Depends(get_db)
):
    """
    创建新任务
    
    Args:
        task_data: 任务创建数据
        db: 数据库会话
        
    Returns:
        TaskResponse: 创建的任务信息
    """
    
    try:
        task = task_service.create_task(task_data.user_context, db)
        return TaskResponse.from_orm(task)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"任务创建失败: {str(e)}")


@router.get("/list", response_model=TaskList)
async def get_task_list(
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """
    获取任务列表
    
    Args:
        skip: 跳过的记录数
        limit: 返回的记录数限制
        db: 数据库会话
        
    Returns:
        TaskList: 任务列表和总数
    """
    
    tasks = task_service.get_task_list(db, skip, limit)
    total = task_service.get_task_count(db)
    
    return TaskList(
        tasks=[TaskResponse.from_orm(task) for task in tasks],
        total=total
    )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    获取单个任务详情
    
    Args:
        task_id: 任务ID
        db: 数据库会话
        
    Returns:
        TaskResponse: 任务详情
        
    Raises:
        HTTPException: 任务不存在
    """
    
    task = task_service.get_task_by_id(task_id, db)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return TaskResponse.from_orm(task)


@router.post("/{task_id}/process")
async def process_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    处理任务 - 流式生成PRD和测试用例
    
    这个接口使用Server-Sent Events (SSE) 实现流式输出
    客户端可以实时接收生成的内容
    
    Args:
        task_id: 任务ID
        db: 数据库会话
        
    Returns:
        StreamingResponse: SSE流式响应
        
    Raises:
        HTTPException: 任务不存在或状态不正确
    """
    
    # 验证任务存在
    task = task_service.get_task_by_id(task_id, db)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 检查任务状态
    if task.status.value == "processing":
        raise HTTPException(status_code=400, detail="任务正在处理中")
    
    if task.status.value == "completed":
        raise HTTPException(status_code=400, detail="任务已完成")
    
    # 检查是否有上传的文件
    if not task.uploaded_files:
        raise HTTPException(status_code=400, detail="任务没有关联的上传文件")
    
    async def generate_stream():
        """
        生成SSE流式数据
        
        SSE格式说明：
        - 每行以 "data: " 开头
        - JSON数据包含type和content字段
        - 以空行分隔每个事件
        """
        try:
            async for content in task_service.process_task_stream(task, db):
                # 将GeneratedContent转换为JSON
                data = {
                    "type": content.type,
                    "content": content.content,
                    "is_complete": content.is_complete
                }
                
                # SSE格式输出
                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                
        except Exception as e:
            # 错误处理
            error_data = {
                "type": "error",
                "content": f"处理过程中发生错误: {str(e)}",
                "is_complete": True
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.get("/{task_id}/download-excel")
async def download_excel(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    下载任务的Excel报告
    
    Args:
        task_id: 任务ID
        db: 数据库会话
        
    Returns:
        StreamingResponse: Excel文件下载响应
        
    Raises:
        HTTPException: 任务不存在或未完成
    """
    
    # 验证任务存在且已完成
    task = task_service.get_task_by_id(task_id, db)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    if task.status.value != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成，无法下载报告")
    
    if not task.generated_prd or not task.generated_test_cases:
        raise HTTPException(status_code=400, detail="任务没有生成完整的内容")
    
    try:
        # 解析测试用例
        test_cases = task_service.parse_test_cases_json(task.generated_test_cases)
        
        # 生成Excel文件
        excel_buffer = excel_service.generate_task_report(
            task=task,
            test_cases=test_cases
        )
        
        # 设置文件名
        filename = f"测试报告_任务{task.id}_{task.created_at.strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return StreamingResponse(
            excel_buffer,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{filename}"
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Excel生成失败: {str(e)}")


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    db: Session = Depends(get_db)
):
    """
    删除任务
    
    Args:
        task_id: 任务ID
        db: 数据库会话
        
    Returns:
        dict: 删除结果
        
    Raises:
        HTTPException: 任务不存在
    """
    
    task = task_service.get_task_by_id(task_id, db)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    try:
        # 删除任务（级联删除相关文件）
        db.delete(task)
        db.commit()
        
        return {"message": "任务删除成功"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"任务删除失败: {str(e)}")
