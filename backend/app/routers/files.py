"""
文件相关API路由
处理文件上传、下载、删除等操作
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List
import os

from app.db.database import get_db
from app.services.file_service import file_service
from app.models.file_upload import FileUpload
from app.schemas.task import FileUploadInfo


router = APIRouter()


@router.post("/upload/{task_id}", response_model=List[FileUploadInfo])
async def upload_files(
    task_id: int,
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db)
):
    """
    上传文件接口
    
    支持同时上传多个文件，所有文件都会关联到指定的任务
    
    Args:
        task_id: 任务ID
        files: 上传的文件列表
        db: 数据库会话
        
    Returns:
        List[FileUploadInfo]: 上传成功的文件信息列表
        
    Raises:
        HTTPException: 任务不存在或文件上传失败
    """
    
    # 验证任务是否存在
    from app.models.task import Task
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 验证文件数量
    if len(files) == 0:
        raise HTTPException(status_code=400, detail="请至少上传一个文件")
    
    if len(files) > 10:  # 限制最多上传10个文件
        raise HTTPException(status_code=400, detail="最多只能同时上传10个文件")
    
    try:
        # 保存所有文件
        saved_files = await file_service.save_multiple_files(files, task, db)
        
        # 转换为响应模型
        return [FileUploadInfo.from_orm(file_record) for file_record in saved_files]
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败：{str(e)}")


@router.get("/download/{file_id}")
async def download_file(
    file_id: int,
    db: Session = Depends(get_db)
):
    """
    下载文件接口
    
    Args:
        file_id: 文件ID
        db: 数据库会话
        
    Returns:
        FileResponse: 文件下载响应
        
    Raises:
        HTTPException: 文件不存在
    """
    
    # 查找文件记录
    file_record = db.query(FileUpload).filter(FileUpload.id == file_id).first()
    if not file_record:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    # 检查文件是否存在于磁盘
    if not os.path.exists(file_record.file_path):
        raise HTTPException(status_code=404, detail="文件已被删除")
    
    # 返回文件
    return FileResponse(
        path=file_record.file_path,
        filename=file_record.original_filename,
        media_type=file_record.mime_type
    )


@router.delete("/delete/{file_id}")
async def delete_file(
    file_id: int,
    db: Session = Depends(get_db)
):
    """
    删除文件接口
    
    Args:
        file_id: 文件ID
        db: 数据库会话
        
    Returns:
        dict: 删除结果
        
    Raises:
        HTTPException: 文件不存在或删除失败
    """
    
    # 查找文件记录
    file_record = db.query(FileUpload).filter(FileUpload.id == file_id).first()
    if not file_record:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    # 删除文件
    success = file_service.delete_file(file_record, db)
    if not success:
        raise HTTPException(status_code=500, detail="文件删除失败")
    
    return {"message": "文件删除成功"}


@router.get("/info/{file_id}", response_model=FileUploadInfo)
async def get_file_info(
    file_id: int,
    db: Session = Depends(get_db)
):
    """
    获取文件信息接口
    
    Args:
        file_id: 文件ID
        db: 数据库会话
        
    Returns:
        FileUploadInfo: 文件信息
        
    Raises:
        HTTPException: 文件不存在
    """
    
    file_record = db.query(FileUpload).filter(FileUpload.id == file_id).first()
    if not file_record:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    return FileUploadInfo.from_orm(file_record)
