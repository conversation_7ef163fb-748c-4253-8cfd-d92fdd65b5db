"""
数据库配置模块
负责SQLAlchemy引擎创建、会话管理和数据库初始化
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator

from app.core.config import settings


# 创建SQLAlchemy引擎
# SQLite使用文件数据库，check_same_thread=False允许多线程访问
engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False},  # SQLite特有配置
    echo=settings.debug  # 开发模式下打印SQL语句
)

# 创建会话工厂
# autocommit=False: 需要手动提交事务
# autoflush=False: 需要手动刷新到数据库
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
# 所有数据库模型都将继承这个基类
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    数据库会话依赖注入函数
    
    这是FastAPI的依赖注入模式：
    1. 创建数据库会话
    2. 在请求处理过程中提供会话
    3. 请求结束后自动关闭会话
    
    使用方式：
    @app.get("/api/example")
    def example_endpoint(db: Session = Depends(get_db)):
        # 在这里使用db进行数据库操作
        pass
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db() -> None:
    """
    初始化数据库
    创建所有表结构
    
    这个函数会在应用启动时调用，确保数据库表存在
    """
    # 导入所有模型，确保它们被注册到Base.metadata中
    from app.models import task, file_upload  # noqa
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
