"""
核心配置模块
负责管理应用的所有配置项，包括数据库连接、API密钥、文件路径等
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """
    应用配置类
    使用Pydantic Settings进行配置管理，支持从环境变量读取配置
    """
    
    # 应用基础配置
    app_name: str = "软件测试自动化平台系统"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 数据库配置
    # SQLite数据库文件路径，相对于项目根目录
    database_url: str = "sqlite:///./testing_platform.db"
    
    # 文件上传配置
    upload_dir: str = "uploads"  # 上传文件存储目录
    max_file_size: int = 10 * 1024 * 1024  # 最大文件大小：10MB
    allowed_extensions: list = [".png", ".jpg", ".jpeg", ".xmind", ".vsdx"]
    
    # LLM API配置
    # 支持多种LLM服务，这里以OpenAI为例
    openai_api_key: Optional[str] = None
    openai_base_url: str = "https://api.openai.com/v1"
    
    # 也可以支持其他LLM服务
    gemini_api_key: Optional[str] = None
    claude_api_key: Optional[str] = None
    
    # 默认使用的LLM服务
    default_llm_provider: str = "openai"
    
    # 跨域配置
    cors_origins: list = [
        "http://localhost:3000",  # Vue开发服务器
        "http://localhost:8080",  # 备用前端端口
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
    ]
    
    class Config:
        # 从.env文件读取环境变量
        env_file = ".env"
        env_file_encoding = "utf-8"


# 创建全局配置实例
settings = Settings()


def get_upload_path() -> str:
    """
    获取上传文件的完整路径
    如果目录不存在则创建
    """
    upload_path = os.path.join(os.getcwd(), settings.upload_dir)
    os.makedirs(upload_path, exist_ok=True)
    return upload_path
