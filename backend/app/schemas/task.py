"""
任务相关的Pydantic数据验证模型
用于API请求和响应的数据验证和序列化
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from enum import Enum


class TaskStatusEnum(str, Enum):
    """任务状态枚举（Pydantic版本）"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class TaskCreate(BaseModel):
    """
    创建任务的请求模型
    用户提交分析请求时使用
    """
    user_context: str = Field(
        ..., 
        description="用户提供的上下文背景信息",
        min_length=1,
        max_length=5000,
        example="这是一个电商平台的用户管理模块，主要功能包括用户注册、登录、个人信息管理等"
    )


class FileUploadInfo(BaseModel):
    """
    文件上传信息模型
    """
    id: int
    original_filename: str
    file_size: int
    file_type: str
    uploaded_at: datetime
    
    class Config:
        from_attributes = True  # 允许从SQLAlchemy模型创建


class TaskResponse(BaseModel):
    """
    任务响应模型
    返回任务的完整信息
    """
    id: int
    status: TaskStatusEnum
    created_at: datetime
    updated_at: datetime
    user_context: str
    generated_prd: Optional[str] = None
    generated_test_cases: Optional[str] = None
    error_message: Optional[str] = None
    uploaded_files: List[FileUploadInfo] = []
    
    class Config:
        from_attributes = True


class TaskList(BaseModel):
    """
    任务列表响应模型
    """
    tasks: List[TaskResponse]
    total: int


class TestCase(BaseModel):
    """
    单个测试用例模型
    """
    case_id: str = Field(..., description="用例ID")
    module: str = Field(..., description="所属模块/功能")
    title: str = Field(..., description="用例标题")
    precondition: str = Field(..., description="前置条件")
    steps: List[str] = Field(..., description="操作步骤")
    expected_result: str = Field(..., description="预期结果")


class GeneratedContent(BaseModel):
    """
    生成内容的流式响应模型
    """
    type: str = Field(..., description="内容类型：prd 或 test_cases")
    content: str = Field(..., description="生成的内容")
    is_complete: bool = Field(False, description="是否生成完成")
