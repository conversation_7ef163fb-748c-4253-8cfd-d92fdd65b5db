"""
任务数据模型
存储每次PRD和测试用例生成任务的信息
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.db.database import Base


class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败


class Task(Base):
    """
    任务模型
    
    每次用户上传文件并请求生成PRD和测试用例时，都会创建一个Task记录
    这个模型存储了任务的完整生命周期信息
    """
    __tablename__ = "tasks"
    
    # 主键：任务唯一标识
    id = Column(Integer, primary_key=True, index=True, comment="任务ID")
    
    # 任务基本信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 任务状态
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, comment="任务状态")
    
    # 用户输入信息
    user_context = Column(Text, comment="用户提供的上下文背景信息")
    
    # LLM生成的内容
    generated_prd = Column(Text, comment="生成的产品需求文档内容")
    generated_test_cases = Column(Text, comment="生成的测试用例JSON数据")
    
    # 错误信息（如果任务失败）
    error_message = Column(Text, comment="错误信息")
    
    # 关联关系：一个任务可以有多个上传文件
    uploaded_files = relationship("FileUpload", back_populates="task", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Task(id={self.id}, status={self.status.value}, created_at={self.created_at})>"
