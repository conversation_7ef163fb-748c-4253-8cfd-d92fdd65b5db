"""
文件上传数据模型
存储用户上传文件的元数据信息
"""

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.database import Base


class FileUpload(Base):
    """
    文件上传模型
    
    存储用户上传的文件元数据，包括思维导图、流程图、界面截图等
    每个文件都关联到一个特定的任务
    """
    __tablename__ = "file_uploads"
    
    # 主键：文件唯一标识
    id = Column(Integer, primary_key=True, index=True, comment="文件ID")
    
    # 文件基本信息
    original_filename = Column(String(255), nullable=False, comment="原始文件名")
    stored_filename = Column(String(255), nullable=False, comment="存储文件名（通常是UUID）")
    file_path = Column(String(500), nullable=False, comment="文件存储路径")
    file_size = Column(BigInteger, nullable=False, comment="文件大小（字节）")
    file_type = Column(String(50), nullable=False, comment="文件类型/扩展名")
    mime_type = Column(String(100), comment="MIME类型")
    
    # 时间信息
    uploaded_at = Column(DateTime, default=datetime.utcnow, comment="上传时间")
    
    # 外键：关联到任务
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False, comment="关联任务ID")
    
    # 关联关系：多个文件属于一个任务
    task = relationship("Task", back_populates="uploaded_files")
    
    def __repr__(self):
        return f"<FileUpload(id={self.id}, filename={self.original_filename}, task_id={self.task_id})>"
